#!/usr/bin/env python3
"""
Script de prueba para verificar el análisis de resultados mejorado.
"""

import os
import sys
from unittest.mock import Mock
from src.Utilities.test_executor import TestExecutor

def create_mock_history(final_result, errors=None, extracted_content=None):
    """Crea un objeto mock de historial para pruebas."""
    mock_history = Mock()
    mock_history.final_result.return_value = final_result
    mock_history.errors.return_value = errors or []
    mock_history.extracted_content.return_value = extracted_content or []
    mock_history.urls.return_value = ["https://example.com"]
    mock_history.action_names.return_value = ["navigate", "click", "done"]
    return mock_history

def test_successful_scenarios():
    """Prueba escenarios exitosos."""
    print("=== Probando Escenarios Exitosos ===")
    
    executor = TestExecutor(api_key="test_key")
    
    # Caso 1: Resultado exitoso explícito
    history1 = create_mock_history("Test completed successfully")
    result1 = executor._analyze_test_result(history1, "Feature: Test")
    print(f"Caso 1 - Éxito explícito: {result1['status']} - {result1['message']}")
    
    # Caso 2: Resultado con palabras clave de éxito
    history2 = create_mock_history("I found the element and verified it works correctly")
    result2 = executor._analyze_test_result(history2, "Feature: Test")
    print(f"Caso 2 - Palabras clave de éxito: {result2['status']} - {result2['message']}")
    
    # Caso 3: Resultado en español
    history3 = create_mock_history("La prueba se completó exitosamente y se encontró el elemento")
    result3 = executor._analyze_test_result(history3, "Feature: Test")
    print(f"Caso 3 - Éxito en español: {result3['status']} - {result3['message']}")

def test_failed_scenarios():
    """Prueba escenarios fallidos."""
    print("\n=== Probando Escenarios Fallidos ===")
    
    executor = TestExecutor(api_key="test_key")
    
    # Caso 1: Resultado con palabras clave de fallo
    history1 = create_mock_history("Test failed because the element was not found")
    result1 = executor._analyze_test_result(history1, "Feature: Test")
    print(f"Caso 1 - Fallo explícito: {result1['status']} - {result1['message']}")
    
    # Caso 2: Sin resultado final
    history2 = create_mock_history(None)
    result2 = executor._analyze_test_result(history2, "Feature: Test")
    print(f"Caso 2 - Sin resultado: {result2['status']} - {result2['message']}")
    
    # Caso 3: Muchos errores
    errors = [
        "Error executing action input_text: Failed to input text",
        "Error executing action click_element: Element not found",
        "Error executing action navigate: Timeout",
        None,
        "Error executing action verify: Element not visible"
    ]
    history3 = create_mock_history("Could not complete the test", errors=errors)
    result3 = executor._analyze_test_result(history3, "Feature: Test")
    print(f"Caso 3 - Múltiples errores: {result3['status']} - {result3['message']}")
    print(f"   Detalles: {result3['details']['failure_reason']}")

def test_edge_cases():
    """Prueba casos límite."""
    print("\n=== Probando Casos Límite ===")
    
    executor = TestExecutor(api_key="test_key")
    
    # Caso 1: Resultado muy corto
    history1 = create_mock_history("OK")
    result1 = executor._analyze_test_result(history1, "Feature: Test")
    print(f"Caso 1 - Resultado corto: {result1['status']} - {result1['message']}")
    
    # Caso 2: Resultado ambiguo con algunos errores
    errors = ["Error executing action: Minor issue", None]
    history2 = create_mock_history("The test ran but encountered some issues", errors=errors)
    result2 = executor._analyze_test_result(history2, "Feature: Test")
    print(f"Caso 2 - Resultado ambiguo: {result2['status']} - {result2['message']}")
    
    # Caso 3: Resultado descriptivo sin palabras clave claras
    history3 = create_mock_history("The automation agent navigated to the page and interacted with various elements as requested")
    result3 = executor._analyze_test_result(history3, "Feature: Test")
    print(f"Caso 3 - Resultado descriptivo: {result3['status']} - {result3['message']}")

def test_error_analysis():
    """Prueba el análisis específico de errores."""
    print("\n=== Probando Análisis de Errores ===")
    
    executor = TestExecutor(api_key="test_key")
    
    # Caso 1: Errores de timeout
    timeout_errors = [
        "Error executing action: Timeout waiting for element",
        "Error executing action: Page load timeout",
        None,
        "Error executing action: Element interaction timeout"
    ]
    history1 = create_mock_history(None, errors=timeout_errors)
    result1 = executor._analyze_test_result(history1, "Feature: Test")
    print(f"Caso 1 - Errores de timeout: {result1['message']}")
    
    # Caso 2: Errores de elementos no encontrados
    element_errors = [
        "Error executing action: Element not found",
        "Error executing action: Failed to locate element",
        None,
        "Error executing action: Element not visible"
    ]
    history2 = create_mock_history(None, errors=element_errors)
    result2 = executor._analyze_test_result(history2, "Feature: Test")
    print(f"Caso 2 - Errores de elementos: {result2['message']}")
    
    # Caso 3: Errores de red
    network_errors = [
        "Error executing action: Network connection failed",
        "Error executing action: Connection timeout",
        None
    ]
    history3 = create_mock_history(None, errors=network_errors)
    result3 = executor._analyze_test_result(history3, "Feature: Test")
    print(f"Caso 3 - Errores de red: {result3['message']}")

if __name__ == "__main__":
    print("Iniciando pruebas del análisis de resultados mejorado...\n")
    
    # Configurar variable de entorno para la API key
    os.environ["GOOGLE_API_KEY"] = "test_key"
    
    try:
        test_successful_scenarios()
        test_failed_scenarios()
        test_edge_cases()
        test_error_analysis()
        
        print("\n=== Resumen ===")
        print("✅ Todas las pruebas del análisis de resultados completadas")
        print("✅ El sistema ahora puede distinguir entre éxitos y fallos")
        print("✅ Se proporcionan mensajes detallados para cada tipo de resultado")
        print("✅ El análisis de errores categoriza los problemas correctamente")
        
    except Exception as e:
        print(f"❌ Error durante las pruebas: {str(e)}")
        sys.exit(1)
