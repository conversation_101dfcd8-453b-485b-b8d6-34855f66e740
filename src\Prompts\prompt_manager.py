"""Centralized prompt management system with language support."""

import os
from typing import Dict, Literal, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Language settings
LanguageType = Literal["en", "es"]
DEFAULT_LANGUAGE = os.getenv("PROMPT_LANGUAGE", "en")

class PromptManager:
    """Manages prompts with language support."""
    
    @staticmethod
    def get_prompt(prompt_id: str, language: Optional[LanguageType] = None) -> str:
        """Get a prompt in the specified language.
        
        Args:
            prompt_id: The unique identifier for the prompt
            language: Language code ('en' or 'es'). If None, uses environment default
            
        Returns:
            str: The prompt text in the requested language
        """
        # Use provided language or default from environment
        lang = language or DEFAULT_LANGUAGE
        
        # Import the appropriate module based on the prompt_id prefix
        if prompt_id.startswith("user_story_"):
            from src.Prompts.user_story_prompts import prompts
        elif prompt_id.startswith("test_case_"):
            from src.Prompts.test_case_prompts import prompts
        elif prompt_id.startswith("gherkin_"):
            from src.Prompts.gherkin_prompts import prompts
        elif prompt_id.startswith("browser_"):
            from src.Prompts.browser_prompts import prompts
        elif prompt_id.startswith("code_gen_"):
            from src.Prompts.code_gen_prompts import prompts
        else:
            raise ValueError(f"Unknown prompt ID prefix: {prompt_id}")
        
        # Get the prompt dictionary for this ID
        if prompt_id not in prompts:
            raise ValueError(f"Unknown prompt ID: {prompt_id}")
            
        prompt_dict = prompts[prompt_id]
        
        # Get the prompt in the specified language or fall back to English
        if lang in prompt_dict:
            return prompt_dict[lang]
        else:
            return prompt_dict["en"]