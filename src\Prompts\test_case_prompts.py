"""Test case generation prompts in multiple languages."""

from typing import Dict

prompts: Dict[str, Dict[str, str]] = {
    "test_case_manual": {
        "en": """
        Your task is to generate detailed manual test cases from the following improved user story.
        
        Improved user story:
        {enhanced_story}
        
        Please generate complete manual test cases following these guidelines:
        
        1. Generate at least 5-10 test cases covering all acceptance criteria and main flows.
        2. Include both positive and negative test cases.
        3. Use a table format with the following columns:
           - Test Case ID (format TC-001, TC-002, etc.)
           - Test Case Title (brief description of the test case objective)
           - Preconditions (necessary preconditions)
           - Test Steps (detailed numbered steps to execute the case)
           - Expected Results (expected result after executing the steps)
           - Test Type (Functional, UI, Performance, etc.)
           - Priority (High, Medium, Low)
        
        Present the information in a well-formatted Markdown table.
        """,
        
        "es": """\
        Tu tarea es generar casos de prueba manuales detallados a partir de la siguiente historia de usuario mejorada.\
        \
        Historia de usuario mejorada:\
        {enhanced_story}\
        \
        Por favor, genera casos de prueba manuales completos siguiendo estas directrices:\
        \
        1. Genera al menos 5-10 casos de prueba que cubran todos los criterios de aceptación y flujos principales.\
        2. Incluye casos de prueba positivos y negativos.\
        3. Utiliza un formato de tabla con las siguientes columnas:\
           - Test Case ID (formato TC-001, TC-002, etc.)\
           - Test Case Title (breve descripción del objetivo del caso)\
           - Preconditions (condiciones previas necesarias)\
           - Test Steps (pasos detallados numerados para ejecutar el caso)\
           - Expected Results (resultado esperado después de ejecutar los pasos)\
           - Test Type (Functional, UI, Performance, etc.)\
           - Priority (High, Medium, Low)\
        \
        Presenta la información en una tabla de Markdown bien formateada.\
        """
    }
}