# API Documentation - QA Agent

## Resumen

La API de QA Agent ahora expone toda la funcionalidad de gestión de proyectos, suites de pruebas y casos de prueba, siguiendo el mismo patrón arquitectónico que se usa en la interfaz de usuario.

## Arquitectura

### Patrón Implementado

La API sigue el patrón de **Separación de Responsabilidades** con:

1. **Capa de Presentación**: Endpoints REST modulares
2. **Capa de Servicio**: `TestService` con mixins especializados
3. **Capa de Datos**: `ProjectManagerService` para persistencia

### Estructura Modular

```
src/API/
├── api.py              # API principal con endpoints legacy
├── models.py           # Modelos Pydantic para validación
├── project_routes.py   # Endpoints para proyectos
├── suite_routes.py     # Endpoints para suites
└── testcase_routes.py  # Endpoints para casos de prueba

src/Core/
├── test_service.py           # Servicio principal
└── test_service_extensions.py # Mixins especializados
```

## Endpoints Disponibles

### 🏗️ Gestión de Proyectos

#### Crear Proyecto
```http
POST /api/projects/
Content-Type: application/json

{
  "name": "Mi Proyecto",
  "description": "Descripción del proyecto",
  "tags": ["web", "e2e"]
}
```

#### Listar Proyectos
```http
GET /api/projects/
```

#### Obtener Proyecto
```http
GET /api/projects/{project_id}
```

#### Actualizar Proyecto
```http
PUT /api/projects/{project_id}
Content-Type: application/json

{
  "name": "Nuevo nombre",
  "description": "Nueva descripción"
}
```

#### Eliminar Proyecto
```http
DELETE /api/projects/{project_id}
```

### 📋 Gestión de Suites de Pruebas

#### Crear Suite
```http
POST /api/projects/{project_id}/suites/
Content-Type: application/json

{
  "name": "Suite de Login",
  "description": "Pruebas de autenticación",
  "tags": ["login", "auth"]
}
```

#### Obtener Suite
```http
GET /api/projects/{project_id}/suites/{suite_id}
```

#### Actualizar Suite
```http
PUT /api/projects/{project_id}/suites/{suite_id}
Content-Type: application/json

{
  "name": "Nuevo nombre de suite"
}
```

#### Eliminar Suite
```http
DELETE /api/projects/{project_id}/suites/{suite_id}
```

#### Ejecutar Suite Completa
```http
POST /api/projects/{project_id}/suites/{suite_id}/execute
```

### 🧪 Gestión de Casos de Prueba

#### Crear Caso de Prueba
```http
POST /api/projects/{project_id}/suites/{suite_id}/tests/
Content-Type: application/json

{
  "name": "Login con credenciales válidas",
  "description": "Verificar login exitoso",
  "instrucciones": "1. Navegar a login\n2. Ingresar credenciales\n3. Hacer click en login",
  "historia_de_usuario": "Como usuario quiero poder autenticarme",
  "gherkin": "Given que estoy en la página de login\nWhen ingreso credenciales válidas\nThen debo ser redirigido al dashboard",
  "url": "https://example.com/login",
  "tags": ["smoke", "critical"]
}
```

#### Obtener Caso de Prueba
```http
GET /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}
```

#### Actualizar Caso de Prueba
```http
PUT /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}
Content-Type: application/json

{
  "name": "Nuevo nombre",
  "instrucciones": "Nuevas instrucciones"
}
```

#### Actualizar Estado del Caso
```http
PATCH /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}/status
Content-Type: application/json

{
  "status": "Passed"
}
```

#### Eliminar Caso de Prueba
```http
DELETE /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}
```

#### Ejecutar Caso de Prueba
```http
POST /api/projects/{project_id}/suites/{suite_id}/tests/{test_id}/execute
```

### 🚀 Ejecución de Pruebas (Legacy - Mantiene compatibilidad)

#### Smoke Test
```http
POST /api/tests/smoke
Content-Type: application/json

{
  "instructions": "Verificar que la página principal carga correctamente",
  "url": "https://example.com",
  "user_story": "Como usuario quiero acceder a la página principal"
}
```

#### Test Completo
```http
POST /api/tests/full
Content-Type: application/json

{
  "gherkin_scenario": "Given que estoy en la página principal\nWhen hago click en el botón de login\nThen debo ver el formulario de login",
  "url": "https://example.com"
}
```

### 🛠️ Generación y Utilidades

#### Generar Gherkin
```http
POST /api/generate/gherkin
Content-Type: application/json

{
  "instructions": "Probar el login",
  "url": "https://example.com/login",
  "user_story": "Como usuario quiero autenticarme"
}
```

#### Generar Código
```http
POST /api/generate/code
Content-Type: application/json

{
  "framework": "selenium",
  "gherkin_scenario": "Given...",
  "test_history": {...}
}
```

#### Mejorar Historia de Usuario
```http
POST /api/stories/enhance
Content-Type: application/json

{
  "user_story": "Quiero hacer login"
}
```

## Respuestas de la API

### Respuesta Exitosa - Proyecto
```json
{
  "project_id": "uuid-here",
  "name": "Mi Proyecto",
  "description": "Descripción del proyecto",
  "tags": ["web", "e2e"],
  "created_at": "2024-01-01T10:00:00",
  "updated_at": "2024-01-01T10:00:00",
  "test_suites": {}
}
```

### Respuesta de Lista
```json
{
  "success": true,
  "count": 2,
  "items": [
    {...},
    {...}
  ]
}
```

### Respuesta de Error
```json
{
  "success": false,
  "error": "Descripción del error",
  "details": "Detalles adicionales (opcional)"
}
```

### Respuesta de Ejecución de Suite
```json
{
  "success": true,
  "suite_id": "uuid-here",
  "suite_name": "Suite de Login",
  "total_tests": 5,
  "passed": 4,
  "failed": 1,
  "results": [
    {
      "test_id": "uuid-here",
      "test_name": "Login válido",
      "result": {
        "success": true,
        "test_id": "timestamp",
        "history_path": "path/to/history.json"
      }
    }
  ],
  "execution_time": "2024-01-01T10:00:00"
}
```

## Códigos de Estado HTTP

- `200 OK`: Operación exitosa
- `201 Created`: Recurso creado exitosamente
- `400 Bad Request`: Datos de entrada inválidos
- `404 Not Found`: Recurso no encontrado
- `500 Internal Server Error`: Error interno del servidor

## Autenticación

Actualmente la API no requiere autenticación, pero se recomienda implementar autenticación para uso en producción.

## Ejemplos de Uso

### Flujo Completo: Crear Proyecto → Suite → Caso → Ejecutar

```bash
# 1. Crear proyecto
PROJECT_ID=$(curl -X POST "http://localhost:8000/api/projects/" \
  -H "Content-Type: application/json" \
  -d '{"name": "Proyecto Demo", "description": "Demo API"}' \
  | jq -r '.project_id')

# 2. Crear suite
SUITE_ID=$(curl -X POST "http://localhost:8000/api/projects/$PROJECT_ID/suites/" \
  -H "Content-Type: application/json" \
  -d '{"name": "Suite Demo", "description": "Suite de demostración"}' \
  | jq -r '.suite_id')

# 3. Crear caso de prueba
TEST_ID=$(curl -X POST "http://localhost:8000/api/projects/$PROJECT_ID/suites/$SUITE_ID/tests/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Demo",
    "instrucciones": "Verificar página principal",
    "url": "https://example.com"
  }' \
  | jq -r '.test_id')

# 4. Ejecutar caso de prueba
curl -X POST "http://localhost:8000/api/projects/$PROJECT_ID/suites/$SUITE_ID/tests/$TEST_ID/execute"
```

## Ventajas de la Nueva Arquitectura

1. **Modularidad**: Código organizado en módulos especializados
2. **Reutilización**: Misma lógica para UI y API
3. **Mantenibilidad**: Fácil de extender y mantener
4. **Testabilidad**: Cada componente puede probarse independientemente
5. **Escalabilidad**: Fácil agregar nuevas funcionalidades

## Próximos Pasos

1. Implementar autenticación y autorización
2. Agregar paginación para listas grandes
3. Implementar filtros y búsqueda
4. Agregar webhooks para notificaciones
5. Implementar rate limiting
