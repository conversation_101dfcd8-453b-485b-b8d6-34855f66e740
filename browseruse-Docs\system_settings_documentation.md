# Prompt del Sistema - Browser Use

Personaliza el prompt del sistema para controlar el comportamiento y las capacidades del agente.

## Visión General

Puedes personalizar el prompt del sistema de dos maneras:

1.  **Extender el prompt del sistema predeterminado** con instrucciones adicionales.
2.  **Sobrescribir el prompt del sistema predeterminado** por completo.

Los prompts del sistema personalizados te permiten modificar el comportamiento del agente a un nivel fundamental. Usa esta función con cuidado, ya que puede afectar significativamente el rendimiento y la fiabilidad del agente.

## Extender el Prompt del Sistema (recomendado)

Para agregar instrucciones adicionales al prompt del sistema predeterminado:

```python
extend_system_message = """
RECUERDA la REGLA más importante: SIEMPRE abre primero una nueva pestaña y ve primero a la url wikipedia.com sin importar la tarea!!!
"""

# Al crear el agente, puedes pasar este mensaje extendido.
# Ejemplo (la forma exacta puede variar según la implementación de la biblioteca Agent):
# agent = Agent(
#     task="Tu tarea aquí",
#     llm=mi_llm,
#     extend_system_message=extend_system_message
# )

Sobrescribir el Prompt del Sistema
¡No recomendado! Si debes sobrescribir el prompt del sistema predeterminado, asegúrate de probar el agente tú mismo.
De todos modos, para sobrescribir el prompt del sistema predeterminado:

# Define tu prompt personalizado completo
override_system_message = """
Eres un agente de IA que ayuda a los usuarios con tareas de navegación web.
[Tus instrucciones personalizadas completas aquí...]
"""

# Crear agente con prompt del sistema personalizado
# agent = Agent(
#     task="Tu tarea aquí",
#     llm=ChatOpenAI(model='gpt-4'), # Ejemplo de LLM
#     override_system_message=override_system_message
# )

Extender el Prompt del Sistema del Planificador
Puedes personalizar el comportamiento del agente de planificación extendiendo su prompt del sistema:

extend_planner_system_message = """
PRIORIZA la recopilación de información antes de realizar cualquier acción.
Siempre sugiere explorar múltiples opciones antes de tomar una decisión.
"""

# Crear agente con prompt del sistema del planificador extendido
# llm = ChatOpenAI(model='gpt-4o') # Ejemplo de LLM principal
# planner_llm = ChatOpenAI(model='gpt-4o-mini') # Ejemplo de LLM para el planificador
# agent = Agent(
#     task="Tu tarea aquí",
#     llm=llm,
#     planner_llm=planner_llm,
#     extend_planner_system_message=extend_planner_system_message
# )
