# Agent Guidelines for QA AGENT Codebase

## Build/Run/Test Commands
- Run application: `streamlit run app.py`
- Run CLI: `python cli.py [command] [options]`
- Run smoke test: `python cli.py smoke --url [URL] --instructions [INSTRUCTIONS]`
- Generate code: `python cli.py generate-code --history-path [PATH] --framework [FRAMEWORK]`

## Code Style Guidelines
- **Imports**: Group standard library, third-party, and local imports in separate blocks
- **Types**: Use type annotations from `typing` module for function signatures
- **Error Handling**: Use try/except with specific exceptions and handle errors gracefully
- **Documentation**: Use docstrings with Args/Returns sections for all functions
- **Class Structure**: Use Pydantic models for structured data
- **Async Pattern**: Use async/await for browser automation and external operations
- **Testing**: Generate Gherkin scenarios for test specifications

## Code Organization
- `src/`: Main source code modules organized by functionality
- `src/Prompts/`: Contains prompt templates for AI generation
- `src/Utilities/`: Utility functions and service classes
- `src/UI/`: Streamlit UI components
- `tests/`: Generated test files and execution histories