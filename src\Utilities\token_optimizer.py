"""
Utilities for optimizing token consumption in browser automation agents.
"""

import os
from typing import Op<PERSON>, Dict, Any
from browser_use import <PERSON><PERSON><PERSON>, BrowserConfig
from langchain_google_genai import ChatGoogleGenerativeAI


class TokenOptimizer:
    """Utilities for reducing token consumption in browser automation."""
    
    @staticmethod
    def get_optimized_browser_config() -> Dict[str, Any]:
        """
        Returns browser configuration optimized for token consumption.
        
        Returns:
            Dict with optimized browser configuration parameters
        """
        return {
            "viewport_expansion": 100,  # Reduced from default 500
            "include_dynamic_attributes": False,  # Reduce element details
            "highlight_elements": False,  # Disable highlighting
            "wait_for_network_idle_page_load_time": 1.0,  # Faster page loads
            "minimum_wait_page_load_time": 0.1,  # Reduce wait times
            "wait_between_actions": 0.3  # Faster action execution
        }
    
    @staticmethod
    def create_cost_efficient_llm(api_key: Optional[str] = None) -> Any:
        """
        Creates the most cost-efficient LLM based on current configuration.
        
        Args:
            api_key: Optional API key override
            
        Returns:
            Configured LLM instance
        """
        # Use Gemini 2.0 Flash as the most cost-effective option (free tier)
        if not api_key:
            api_key = os.environ.get("GOOGLE_API_KEY")
            
        return ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            api_key=api_key
        )
    
    @staticmethod
    def create_planner_llm(api_key: Optional[str] = None) -> Any:
        """
        Creates a lightweight LLM for planning tasks.
        
        Args:
            api_key: Optional API key override
            
        Returns:
            Lightweight LLM instance for planning
        """
        # Use smaller Gemini model for planning to reduce tokens
        if not api_key:
            api_key = os.environ.get("GOOGLE_API_KEY")
            
        return ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",  # Smaller model for planning
            api_key=api_key
        )
    
    @staticmethod
    def get_memory_config() -> Dict[str, Any]:
        """
        Returns memory configuration optimized for token reduction.
        
        Returns:
            Dict with memory configuration
        """
        return {
            "enable_memory": True,
            "memory_config": {
                "agent_id": "browser_qa_agent",
                "memory_interval": 5,  # Summarize every 5 steps
                "embedder_provider": "huggingface",  # Free embedding provider
                "vector_store_provider": "faiss",
                "vector_store_base_path": "./tmp/mem0"
            }
        }
    
    @staticmethod
    def get_agent_optimization_config() -> Dict[str, Any]:
        """
        Returns agent configuration optimized for token consumption.
        
        Returns:
            Dict with optimized agent configuration
        """
        return {
            "use_vision": False,  # Disable vision to save significant tokens
            "max_actions_per_step": 3,  # Reduce from default 10
            "max_failures": 3,  # Reduce retry attempts
            "planner_interval": 3,  # Plan every 3 steps instead of every step
            "use_vision_for_planner": False,  # Disable vision for planner
        }
    
    @staticmethod
    def estimate_token_savings(use_vision: bool = True, viewport_expansion: int = 500) -> str:
        """
        Estimates token savings from optimizations.
        
        Args:
            use_vision: Whether vision is enabled
            viewport_expansion: Current viewport expansion setting
            
        Returns:
            String describing estimated savings
        """
        savings = []
        
        if use_vision:
            savings.append("Disabling vision: ~40-60% token reduction")
        
        if viewport_expansion > 200:
            savings.append(f"Reducing viewport_expansion from {viewport_expansion} to 100: ~20-30% token reduction")
        
        savings.append("Using Gemini 2.0 Flash exclusively: Free tier with optimal performance")
        savings.append("Using Gemini 1.5 Flash for planning: Lightweight model for reduced planning tokens")
        savings.append("Enabling memory summarization: ~30-50% token reduction for long sessions")
        savings.append("Automatic initial URL navigation: Eliminates 1-2 manual steps per test (~10-20% reduction)")
        savings.append("Optimized browser settings: Faster execution with reduced element details")
        
        return "\n".join([f"• {saving}" for saving in savings])
    

def apply_token_optimizations() -> Dict[str, Any]:
    """
    Applies all available token optimizations and returns the configuration.
    
    Returns:
        Complete optimized configuration dictionary
    """
    config = {}
    
    # Browser optimizations
    config.update(TokenOptimizer.get_optimized_browser_config())
    
    # Agent optimizations  
    config.update(TokenOptimizer.get_agent_optimization_config())
    
    # Memory optimizations
    config.update(TokenOptimizer.get_memory_config())
    
    # Add LLM instances
    config["llm"] = TokenOptimizer.create_cost_efficient_llm()
    config["planner_llm"] = TokenOptimizer.create_planner_llm()
    
    return config
