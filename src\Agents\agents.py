"""<PERSON><PERSON><PERSON><PERSON> que contiene agentes especializados para diferentes tareas."""

from typing import Dict, Any, Optional, List, Literal
from langchain_google_genai import ChatGoogleGenerativeAI
from browser_use import Browser, Agent as BrowserAgent
import os
from dotenv import load_dotenv

from src.Utilities.utils import controller
from src.Prompts.prompt_manager import PromptManager, LanguageType
from src.Utilities.browser_helper import create_and_run_agent

# Load environment variables
load_dotenv()

class StoryAgent:
    """Agente para mejorar historias de usuario y generar escenarios de prueba."""
    
    def __init__(self, api_key: str, language: Optional[LanguageType] = None):
        """Inicializa el agente de historias.
        
        Args:
            api_key: API key para el LLM
            language: Idioma de las respuestas ('en' o 'es')
        """
        self.api_key = api_key
        self.llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=api_key)
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")
    
    def enhance_story(self, user_story: str) -> str:
        """Mejora una historia de usuario.
        
        Args:
            user_story: Historia de usuario original
            
        Returns:
            str: Historia de usuario mejorada
        """
        # Get prompt template from prompt manager with language
        prompt_template = PromptManager.get_prompt("user_story_enhance", self.language)
        
        # Format the prompt with the user story
        prompt = prompt_template.format(user_story=user_story)
        
        # Call the model to enhance the story
        response = self.llm.invoke(prompt)
        enhanced_story = response.content
        
        return enhanced_story
    
    def generate_manual_tests(self, enhanced_story: str) -> str:
        """Genera casos de prueba manuales a partir de una historia mejorada.
        
        Args:
            enhanced_story: Historia de usuario mejorada
            
        Returns:
            str: Casos de prueba manuales en formato markdown
        """
        # Get prompt template from prompt manager with language
        prompt_template = PromptManager.get_prompt("test_case_manual", self.language)
        
        # Format the prompt with the enhanced story
        prompt = prompt_template.format(enhanced_story=enhanced_story)
        
        # Call the model to generate manual test cases
        response = self.llm.invoke(prompt)
        manual_tests = response.content
        
        return manual_tests
    
    def generate_gherkin(self, manual_tests: str) -> str:
        """Genera escenarios Gherkin a partir de casos de prueba manuales.
        
        Args:
            manual_tests: Casos de prueba manuales
            
        Returns:
            str: Escenarios Gherkin
        """
        # Get prompt template from prompt manager with language
        prompt_template = PromptManager.get_prompt("gherkin_generate", self.language)
        
        # Format the prompt with the manual test cases
        prompt = prompt_template.format(manual_test_cases_markdown=manual_tests)
        
        # Call the model to generate Gherkin scenarios
        response = self.llm.invoke(prompt)
        gherkin = response.content
        
        return gherkin

class BrowserAutomationAgent:
    """Agente para automatizar interacciones con navegadores."""
    
    def __init__(self, api_key: str, language: Optional[LanguageType] = None):
        """Inicializa el agente de automatización de navegador.
        
        Args:
            api_key: API key para el LLM
            language: Idioma de las respuestas ('en' o 'es')
        """
        self.api_key = api_key
        self.llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=api_key)
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")
    
    async def execute_scenario(self, scenario: str, browser: Browser) -> Dict[str, Any]:
        """Ejecuta un escenario Gherkin en un navegador.
        
        Args:
            scenario: Escenario Gherkin a ejecutar
            browser: Instancia de Browser
            
        Returns:
            Dict[str, Any]: Resultado de la ejecucion
        """
        # Utilizar la función helper para crear y ejecutar el agente
        history = await create_and_run_agent(
            scenario_text=scenario,
            browser_instance=browser,
            controller_instance=controller, # controller se importa globalmente
            api_key=self.api_key, # Pasar la api_key de la instancia
            language=self.language # Pasar el idioma de la instancia
        )
        
        return {
            "history": history,
            "success": True, # Asumir éxito si no hay excepciones
            "final_result": history.final_result() if hasattr(history, 'final_result') else None
        }