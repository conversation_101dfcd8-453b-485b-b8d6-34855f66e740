import re
from typing import Dict, Any, List, Tuple
import json
import streamlit as st

# Evitamos el import circular removiendo esta importación
# y usando directamente los prompts

# Importamos los agentes pero sin crear circularidad
from langchain_google_genai import ChatGoogleGenerativeAI
import os

from src.Utilities.utils import (
    extract_selectors_from_history,
    analyze_actions)

# Funciones para mantener la compatibilidad con el código existente
def enhance_user_story(user_story: str) -> str:
    """Mejora una historia de usuario para hacerla más clara y completa.
    
    Args:
        user_story: Historia de usuario original
        
    Returns:
        str: Historia de usuario mejorada
    """
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    prompt = f"""
    Tu tarea es mejorar la siguiente historia de usuario para que sea más clara, completa y siga el formato estándar 'Como [rol], quiero [funcionalidad] para [beneficio]'.
    
    Asegúrate de incluir:
    1. El rol específico del usuario (Quién)
    2. La funcionalidad deseada (Qué)
    3. El beneficio o valor esperado (Por qué)
    4. Criterios de aceptación claros y específicos
    5. Ejemplos concretos si es posible
    
    Historia original:
    {user_story}
    
    Proporciona una versión mejorada y expandida que mantenga la intención original pero sea más detallada y útil para los equipos de desarrollo y pruebas.
    """
    
    # Llamar al modelo para mejorar la historia
    response = llm.invoke(prompt)
    return response.content

def generate_manual_test_cases(enhanced_story: str) -> str:
    """Genera casos de prueba manuales a partir de una historia de usuario mejorada.
    
    Args:
        enhanced_story: Historia de usuario mejorada
        
    Returns:
        str: Casos de prueba manuales en formato markdown
    """
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    prompt = f"""
    Tu tarea es generar casos de prueba manuales detallados a partir de la siguiente historia de usuario mejorada.
    
    Historia de usuario mejorada:
    {enhanced_story}
    
    Por favor, genera casos de prueba manuales completos siguiendo estas directrices:
    
    1. Genera al menos 5-10 casos de prueba que cubran todos los criterios de aceptación y flujos principales.
    2. Incluye casos de prueba positivos y negativos.
    3. Utiliza un formato de tabla con las siguientes columnas:
       - Test Case ID (formato TC-001, TC-002, etc.)
       - Test Case Title (breve descripción del objetivo del caso)
       - Preconditions (condiciones previas necesarias)
       - Test Steps (pasos detallados numerados para ejecutar el caso)
       - Expected Results (resultado esperado después de ejecutar los pasos)
       - Test Type (Functional, UI, Performance, etc.)
       - Priority (High, Medium, Low)
    
    Presenta la información en una tabla de Markdown bien formateada.
    """
    
    # Llamar al modelo para generar casos de prueba manuales
    response = llm.invoke(prompt)
    return response.content

def generate_gherkin_scenarios(manual_test_cases_markdown: str) -> str:
    """Generate Gherkin scenarios from manual test cases using the QA agent while preserving URLs"""
    try:
        # Extraer todas las URLs del texto original
        url_pattern = re.compile(
            r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
        )

        # Encontrar todas las URLs en el texto original
        original_urls = url_pattern.findall(manual_test_cases_markdown)

        # Crear un diccionario de reemplazo para restaurar las URLs después
        url_placeholders = {}
        modified_test_cases = manual_test_cases_markdown

        for i, url in enumerate(original_urls):
            placeholder = f"__URL_PLACEHOLDER_{i}__"
            url_placeholders[placeholder] = url
            # Reemplazar la URL con un placeholder para evitar que sea modificada
            modified_test_cases = modified_test_cases.replace(url, placeholder)

        # Añadir instrucciones específicas para preservar URLs
        url_preservation_instructions = ""
        if original_urls:
            url_preservation_instructions = "\n\nIMPORTANTE: Asegúrate de preservar exactamente las siguientes URLs en tus escenarios Gherkin:\n"
            for url in original_urls:
                url_preservation_instructions += f"- {url}\n"

        # Llamar al agente con el texto modificado y las instrucciones adicionales
        run_response = gherkhin_agent.run(modified_test_cases + url_preservation_instructions)

        # Extract the content from the agent's response
        gherkin_content = extract_code_content(run_response.content)

        # Restaurar las URLs originales
        for placeholder, url in url_placeholders.items():
            gherkin_content = gherkin_content.replace(placeholder, url)

        # Verificar si alguna URL no fue preservada
        for url in original_urls:
            if url not in gherkin_content:
                # Si la URL no está en el texto generado, añadirla en un comentario
                gherkin_content = f"# URL original que debe usarse: {url}\n" + gherkin_content

        # Si hay una URL en session_state, asegurarse de que esté incluida
        import streamlit as st
        if 'test_url' in st.session_state and st.session_state.test_url:
            test_url = st.session_state.test_url
            if test_url not in gherkin_content:
                # Buscar líneas que comiencen con "Given" para insertar la URL
                lines = gherkin_content.split('\n')
                for i, line in enumerate(lines):
                    if line.strip().startswith(('Given', 'Dado', 'Dada')):
                        # Si no hay una URL en esta línea, añadirla
                        if 'http' not in line:
                            lines[i] = line.replace(line, f"{line} {test_url}")
                            break
                gherkin_content = '\n'.join(lines)

        return gherkin_content
    except Exception as e:
        st.error(f"Error generating Gherkin scenarios: {str(e)}")
        raise

def generate_manual_test_cases(user_story: str) -> str:
    """Generate manual test cases from a user story using the manual test case agent"""
    try:
        run_response = manual_test_case_agent.run(user_story)
        # The manual test case agent is expected to return markdown
        manual_test_cases_content = extract_code_content(run_response.content)
        return manual_test_cases_content
    except Exception as e:
        st.error(f"Error generating manual test cases: {str(e)}")
        raise

def enhance_user_story(user_story: str) -> str:
    """Enhance a raw user story using the user story enhancement agent while preserving URLs"""
    try:
        # Extraer todas las URLs del texto original
        url_pattern = re.compile(
            r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
        )

        # Encontrar todas las URLs en el texto original
        original_urls = url_pattern.findall(user_story)

        # Crear un diccionario de reemplazo para restaurar las URLs después
        url_placeholders = {}
        for i, url in enumerate(original_urls):
            placeholder = f"__URL_PLACEHOLDER_{i}__"
            url_placeholders[placeholder] = url
            # Reemplazar la URL con un placeholder para evitar que sea modificada
            user_story = user_story.replace(url, placeholder)

        # Llamar al agente con el texto modificado
        run_response = user_story_enhancement_agent.run(user_story)
        enhanced_story_content = run_response.content

        # Restaurar las URLs originales
        for placeholder, url in url_placeholders.items():
            enhanced_story_content = enhanced_story_content.replace(placeholder, url)

        # Verificar si alguna URL no fue preservada (por si el agente eliminó algún placeholder)
        for url in original_urls:
            if url not in enhanced_story_content:
                # Si la URL no está en el texto mejorado, añadirla en una sección especial
                if "## URLs Originales" not in enhanced_story_content:
                    enhanced_story_content += "\n\n## URLs Originales\n"
                enhanced_story_content += f"- {url}\n"

        return enhanced_story_content
    except Exception as e:
        st.error(f"Error enhancing user story: {str(e)}")
        raise

def extract_code_content(text: str) -> str:
    """Extract code from markdown code blocks if present"""
    # Look for content between triple backticks with optional language identifier
    code_block_pattern = re.compile(r"```(?:python|gherkin|javascript|java|robot|markdown)?\n(.*?)```", re.DOTALL)
    match = code_block_pattern.search(text)

    if match:
        return match.group(1).strip()
    return text.strip()

def _get_preserved_urls(gherkin_steps: str, history_data: Dict[str, Any]) -> Tuple[str, List[str]]:
    """
    Función auxiliar para obtener las URLs preservadas del escenario Gherkin y la sesión.
    Retorna la URL base y una lista de todas las URLs encontradas.
    """
    import streamlit as st

    # Extraer todas las URLs del escenario Gherkin
    url_pattern = re.compile(
        r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+(?:/[-\w%!$&\'()*+,;=:@/~]+)*(?:\?[-\w%!$&\'()*+,;=:@/~]*)?(?:#[-\w%!$&\'()*+,;=:@/~]*)?'
    )
    gherkin_urls = url_pattern.findall(gherkin_steps)

    # Obtener URLs del historial
    history_urls = history_data.get('urls', [])

    # Obtener URL de la sesión
    session_url = ""
    if 'test_url' in st.session_state and st.session_state.test_url:
        session_url = st.session_state.test_url

    # Combinar todas las URLs encontradas
    all_urls = list(set(gherkin_urls + history_urls + ([session_url] if session_url else [])))
    all_urls = [url for url in all_urls if url]  # Eliminar URLs vacías

    # Determinar la URL base
    base_url = "https://example.com"  # URL por defecto

    # Prioridad: 1. URL de sesión, 2. Primera URL del historial, 3. Primera URL del Gherkin
    if session_url:
        base_url = session_url
    elif history_urls:
        base_url = history_urls[0]
    elif gherkin_urls:
        base_url = gherkin_urls[0]

    return base_url, all_urls

def generate_selenium_pytest_bdd(gherkin_steps: str, history_data: Dict[str, Any]) -> str:
    """Generate a single Python file with Selenium PyTest BDD automation code using the code generation agent"""

    # Extract feature name from Gherkin (optional, for context)
    feature_match = re.search(r"Feature:\s*(.+?)(?:\n|$)", gherkin_steps)
    feature_name = feature_match.group(1).strip() if feature_match else "Automated Test"

    # Extract selectors and actions
    selectors = extract_selectors_from_history(history_data)
    actions = analyze_actions(history_data)

    # Get preserved URLs
    base_url, all_urls = _get_preserved_urls(gherkin_steps, history_data)

    # Create prompt for Selenium PyTest BDD code
    # The code generation agent's description, instructions, and expected_output handle the code generation logic.
    # We provide the Gherkin steps and execution details as context.
    code_file_prompt = f"""
    Generate Selenium PyTest BDD code based on the following:

    Gherkin Steps:
    ```gherkin
    {gherkin_steps}
    ```

    Agent Execution Details:
    - Base URL: {base_url}
    - Element Selectors: {json.dumps(selectors, indent=2)}
    - Actions Performed: {json.dumps(actions, indent=2)}
    - Extracted Content: {json.dumps(history_data.get('extracted_content', []), indent=2)}
    """

    try:
        # Generate the single file
        code_response = code_gen_agent.run(code_file_prompt)
        code_content = extract_code_content(code_response.content)

        return code_content

    except Exception as e:
        st.error(f"Error generating Selenium PyTest BDD code: {str(e)}")
        raise

def generate_playwright_python(gherkin_steps: str, history_data: Dict[str, Any]) -> str:
    """Generate a single Python file with Playwright automation code using the code generation agent"""

    # Extract feature name from Gherkin (optional, for context)
    feature_match = re.search(r"Feature:\s*(.+?)(?:\n|$)", gherkin_steps)
    feature_name = feature_match.group(1).strip() if feature_match else "Automated Test"

    # Extract selectors and actions
    selectors = extract_selectors_from_history(history_data)
    actions = analyze_actions(history_data)

    # Get preserved URLs
    base_url, all_urls = _get_preserved_urls(gherkin_steps, history_data)

    # Create prompt for Playwright code
    # The code generation agent's description, instructions, and expected_output handle the code generation logic.
    # We provide the Gherkin steps and execution details as context.
    code_file_prompt = f"""
    Generate Playwright Python code based on the following:

    Gherkin Steps:
    ```gherkin
    {gherkin_steps}
    ```

    Agent Execution Details:
    - Base URL: {base_url}
    - Element Selectors: {json.dumps(selectors, indent=2)}
    - Actions Performed: {json.dumps(actions, indent=2)}
    - Extracted Content: {json.dumps(history_data.get('extracted_content', []), indent=2)}
    """

    try:
        # Generate the single file
        code_response = code_gen_agent.run(code_file_prompt)
        code_content = extract_code_content(code_response.content)

        return code_content

    except Exception as e:
        st.error(f"Error generating Playwright code: {str(e)}")
        raise

def generate_cypress_js(gherkin_steps: str, history_data: Dict[str, Any]) -> str:
    """Generate a single JavaScript file with Cypress automation code using the code generation agent"""

    # Extract feature name from Gherkin (optional, for context)
    feature_match = re.search(r"Feature:\s*(.+?)(?:\n|$)", gherkin_steps)
    feature_name = feature_match.group(1).strip() if feature_match else "Automated Test"

    # Extract selectors and actions
    selectors = extract_selectors_from_history(history_data)
    actions = analyze_actions(history_data)

    # Get preserved URLs
    base_url, all_urls = _get_preserved_urls(gherkin_steps, history_data)

    # Create prompt for Cypress code
    # The code generation agent's description, instructions, and expected_output handle the code generation logic.
    # We provide the Gherkin steps and execution details as context.
    code_file_prompt = f"""
    Generate Cypress JavaScript code based on the following:

    Gherkin Steps:
    ```gherkin
    {gherkin_steps}
    ```

    Agent Execution Details:
    - Base URL: {base_url}
    - Element Selectors: {json.dumps(selectors, indent=2)}
    - Actions Performed: {json.dumps(actions, indent=2)}
    - Extracted Content: {json.dumps(history_data.get('extracted_content', []), indent=2)}
    """

    try:
        # Generate the single file
        code_response = code_gen_agent.run(code_file_prompt)
        code_content = extract_code_content(code_response.content)

        return code_content

    except Exception as e:
        st.error(f"Error generating Cypress code: {str(e)}")
        raise

def generate_robot_framework(gherkin_steps: str, history_data: Dict[str, Any]) -> str:
    """Generate Robot Framework test file using the code generation agent"""

    # Extract feature name from Gherkin (optional, for context)
    feature_match = re.search(r"Feature:\s*(.+?)(?:\n|$)", gherkin_steps)
    feature_name = feature_match.group(1).strip() if feature_match else "Automated Test"

    # Extract selectors and actions
    selectors = extract_selectors_from_history(history_data)
    actions = analyze_actions(history_data)

    # Get preserved URLs
    base_url, all_urls = _get_preserved_urls(gherkin_steps, history_data)

    # Create prompt for Robot Framework code
    # The code generation agent's description, instructions, and expected_output handle the code generation logic.
    # We provide the Gherkin steps and execution details as context.
    code_file_prompt = f"""
    Generate Robot Framework code based on the following:

    Gherkin Steps:
    ```gherkin
    {gherkin_steps}
    ```

    Agent Execution Details:
    - Base URL: {base_url}
    - Element Selectors: {json.dumps(selectors, indent=2)}
    - Actions Performed: {json.dumps(actions, indent=2)}
    - Extracted Content: {json.dumps(history_data.get('extracted_content', []), indent=2)}
    """

    try:
        # Generate the single file
        code_response = code_gen_agent.run(code_file_prompt)
        code_content = extract_code_content(code_response.content)

        return code_content

    except Exception as e:
        st.error(f"Error generating Robot Framework code: {str(e)}")
        raise

def generate_java_selenium(gherkin_steps: str, history_data: Dict[str, Any]) -> str:
    """Generate a Java file with Selenium and Cucumber automation code using the code generation agent"""

    # Extract feature name from Gherkin (optional, for context)
    feature_match = re.search(r"Feature:\s*(.+?)(?:\n|$)", gherkin_steps)
    feature_name = feature_match.group(1).strip() if feature_match else "Automated Test"

    # Extract selectors and actions
    selectors = extract_selectors_from_history(history_data)
    actions = analyze_actions(history_data)

    # Get preserved URLs
    base_url, all_urls = _get_preserved_urls(gherkin_steps, history_data)

    # Create prompt for Java Selenium Cucumber code
    # The code generation agent's description, instructions, and expected_output handle the code generation logic.
    # We provide the Gherkin steps and execution details as context.
    code_file_prompt = f"""
    Generate Java Selenium Cucumber code based on the following:

    Gherkin Steps:
    ```gherkin
    {gherkin_steps}
    ```

    Agent Execution Details:
    - Base URL: {base_url}
    - Element Selectors: {json.dumps(selectors, indent=2)}
    - Actions Performed: {json.dumps(actions, indent=2)}
    - Extracted Content: {json.dumps(history_data.get('extracted_content', []), indent=2)}
    """

    try:
        # Generate the single file
        code_response = code_gen_agent.run(code_file_prompt)
        code_content = extract_code_content(code_response.content)

        return code_content

    except Exception as e:
        st.error(f"Error generating Java Selenium Cucumber code: {str(e)}")
        raise
