"""Extensiones del TestService para gestión de suites y casos de prueba."""

from typing import Dict, Any, Optional, List
from datetime import datetime


class TestServiceSuitesMixin:
    """Mixin para gestión de suites de pruebas."""
    
    def create_test_suite(self, project_id: str, name: str, description: str = "", 
                         tags: List[str] = None) -> Optional[Dict[str, Any]]:
        """Crea una nueva suite de pruebas.
        
        Args:
            project_id: ID del proyecto
            name: Nombre de la suite
            description: Descripción de la suite
            tags: Lista de etiquetas
            
        Returns:
            Optional[Dict[str, Any]]: Datos de la suite creada o None si el proyecto no existe
        """
        suite = self.project_manager.create_test_suite(
            project_id=project_id, name=name, description=description, tags=tags or []
        )
        return suite.to_dict() if suite else None
    
    def get_test_suite(self, project_id: str, suite_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene una suite de pruebas por su ID.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            
        Returns:
            Optional[Dict[str, Any]]: Datos de la suite o None si no existe
        """
        suite = self.project_manager.get_test_suite(project_id, suite_id)
        return suite.to_dict() if suite else None
    
    def get_project_suites(self, project_id: str) -> List[Dict[str, Any]]:
        """Obtiene todas las suites de un proyecto.
        
        Args:
            project_id: ID del proyecto
            
        Returns:
            List[Dict[str, Any]]: Lista de suites del proyecto
        """
        project = self.project_manager.get_project(project_id)
        if not project:
            return []
        
        suites = project.get_all_test_suites()
        return [suite.to_dict() for suite in suites]
    
    def update_test_suite(self, project_id: str, suite_id: str, name: str = None, 
                         description: str = None, tags: List[str] = None) -> Optional[Dict[str, Any]]:
        """Actualiza una suite de pruebas existente.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            name: Nuevo nombre (opcional)
            description: Nueva descripción (opcional)
            tags: Nuevas etiquetas (opcional)
            
        Returns:
            Optional[Dict[str, Any]]: Datos de la suite actualizada o None si no existe
        """
        suite = self.project_manager.update_test_suite(
            project_id=project_id, suite_id=suite_id, name=name, 
            description=description, tags=tags
        )
        return suite.to_dict() if suite else None
    
    def delete_test_suite(self, project_id: str, suite_id: str) -> bool:
        """Elimina una suite de pruebas.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            
        Returns:
            bool: True si se eliminó correctamente, False en caso contrario
        """
        return self.project_manager.delete_test_suite(project_id, suite_id)


class TestServiceTestCasesMixin:
    """Mixin para gestión de casos de prueba."""
    
    def create_test_case(self, project_id: str, suite_id: str, name: str, description: str = "",
                        instrucciones: str = "", historia_de_usuario: str = "",
                        gherkin: str = "", url: str = "", tags: List[str] = None) -> Optional[Dict[str, Any]]:
        """Crea un nuevo caso de prueba.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            name: Nombre del caso de prueba
            description: Descripción del caso de prueba
            instrucciones: Instrucciones para ejecutar la prueba
            historia_de_usuario: Historia de usuario
            gherkin: Escenario Gherkin
            url: URL para la prueba
            tags: Lista de etiquetas
            
        Returns:
            Optional[Dict[str, Any]]: Datos del caso de prueba creado o None si la suite no existe
        """
        test_case = self.project_manager.create_test_case(
            project_id=project_id, suite_id=suite_id, name=name, description=description,
            instrucciones=instrucciones, historia_de_usuario=historia_de_usuario,
            gherkin=gherkin, url=url, tags=tags or []
        )
        return test_case.to_dict() if test_case else None
    
    def get_test_case(self, project_id: str, suite_id: str, test_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene un caso de prueba por su ID.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            test_id: ID del caso de prueba
            
        Returns:
            Optional[Dict[str, Any]]: Datos del caso de prueba o None si no existe
        """
        test_case = self.project_manager.get_test_case(project_id, suite_id, test_id)
        return test_case.to_dict() if test_case else None
    
    def get_suite_test_cases(self, project_id: str, suite_id: str) -> List[Dict[str, Any]]:
        """Obtiene todos los casos de prueba de una suite.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            
        Returns:
            List[Dict[str, Any]]: Lista de casos de prueba de la suite
        """
        suite = self.project_manager.get_test_suite(project_id, suite_id)
        if not suite:
            return []
        
        test_cases = suite.get_all_test_cases()
        return [test_case.to_dict() for test_case in test_cases]
    
    def update_test_case(self, project_id: str, suite_id: str, test_id: str,
                        name: str = None, description: str = None,
                        instrucciones: str = None, historia_de_usuario: str = None,
                        gherkin: str = None, url: str = None, 
                        tags: List[str] = None) -> Optional[Dict[str, Any]]:
        """Actualiza un caso de prueba existente.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            test_id: ID del caso de prueba
            name: Nuevo nombre (opcional)
            description: Nueva descripción (opcional)
            instrucciones: Nuevas instrucciones (opcional)
            historia_de_usuario: Nueva historia de usuario (opcional)
            gherkin: Nuevo escenario Gherkin (opcional)
            url: Nueva URL (opcional)
            tags: Nuevas etiquetas (opcional)
            
        Returns:
            Optional[Dict[str, Any]]: Datos del caso de prueba actualizado o None si no existe
        """
        test_case = self.project_manager.update_test_case(
            project_id=project_id, suite_id=suite_id, test_id=test_id,
            name=name, description=description, instrucciones=instrucciones,
            historia_de_usuario=historia_de_usuario, gherkin=gherkin, url=url, tags=tags
        )
        return test_case.to_dict() if test_case else None
    
    def delete_test_case(self, project_id: str, suite_id: str, test_id: str) -> bool:
        """Elimina un caso de prueba.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            test_id: ID del caso de prueba
            
        Returns:
            bool: True si se eliminó correctamente, False en caso contrario
        """
        return self.project_manager.delete_test_case(project_id, suite_id, test_id)
    
    def update_test_case_status(self, project_id: str, suite_id: str, test_id: str, 
                               status: str) -> Optional[Dict[str, Any]]:
        """Actualiza el estado de un caso de prueba.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            test_id: ID del caso de prueba
            status: Nuevo estado
            
        Returns:
            Optional[Dict[str, Any]]: Datos del caso de prueba actualizado o None si no existe
        """
        test_case = self.project_manager.update_test_case_status(project_id, suite_id, test_id, status)
        return test_case.to_dict() if test_case else None


class TestServiceExecutionMixin:
    """Mixin para ejecución de suites y casos de prueba."""
    
    def execute_test_case(self, project_id: str, suite_id: str, test_id: str) -> Dict[str, Any]:
        """Ejecuta un caso de prueba específico.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            test_id: ID del caso de prueba
            
        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        test_case = self.project_manager.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return {"success": False, "error": "Caso de prueba no encontrado"}
        
        try:
            # Ejecutar el smoke test usando los datos del caso de prueba
            result = self.run_smoke_test(
                instructions=test_case.instrucciones,
                url=test_case.url,
                user_story=test_case.historia_de_usuario
            )
            
            # Actualizar el estado del caso de prueba
            status = "Passed" if result.get("success", False) else "Failed"
            self.project_manager.update_test_case_status(project_id, suite_id, test_id, status)
            
            # Agregar historial si existe
            if result.get("history_path"):
                self.project_manager.add_history_to_test_case(
                    project_id, suite_id, test_id, result["history_path"]
                )
            
            return result
            
        except Exception as e:
            # Actualizar estado a fallido
            self.project_manager.update_test_case_status(project_id, suite_id, test_id, "Failed")
            return {"success": False, "error": str(e)}
    
    def execute_test_suite(self, project_id: str, suite_id: str) -> Dict[str, Any]:
        """Ejecuta todos los casos de prueba de una suite.
        
        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            
        Returns:
            Dict[str, Any]: Resultado de la ejecución de la suite
        """
        suite = self.project_manager.get_test_suite(project_id, suite_id)
        if not suite:
            return {"success": False, "error": "Suite de pruebas no encontrada"}
        
        test_cases = suite.get_all_test_cases()
        if not test_cases:
            return {"success": False, "error": "No hay casos de prueba en la suite"}
        
        results = []
        passed = 0
        failed = 0
        
        for test_case in test_cases:
            result = self.execute_test_case(project_id, suite_id, test_case.test_id)
            results.append({
                "test_id": test_case.test_id,
                "test_name": test_case.name,
                "result": result
            })
            
            if result.get("success", False):
                passed += 1
            else:
                failed += 1
        
        return {
            "success": True,
            "suite_id": suite_id,
            "suite_name": suite.name,
            "total_tests": len(test_cases),
            "passed": passed,
            "failed": failed,
            "results": results,
            "execution_time": datetime.now().isoformat()
        }
