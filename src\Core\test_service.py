"""Servicio central para ejecución de pruebas, independiente de la interfaz."""

import os
from typing import Dict, Any, Optional, List
from datetime import datetime

from src.Utilities.test_executor import TestExecutor
from src.Utilities.project_manager_service import ProjectManagerService
from src.Utilities.project_manager import Project, TestSuite, TestCase
from src.Core.test_service_extensions import (
    TestServiceSuitesMixin,
    TestServiceTestCasesMixin,
    TestServiceExecutionMixin
)

class TestService(TestServiceSuitesMixin, TestServiceTestCasesMixin, TestServiceExecutionMixin):
    """Servicio para gestionar la ejecución de pruebas, generación de código y proyectos.

    Esta clase actúa como una capa de servicio que desacopla completamente la lógica
    de negocio de las interfaces de usuario (Streamlit, CLI, API web).
    """

    def __init__(self, api_key: str, language: Optional[str] = None):
        """Inicializa el servicio de pruebas.

        Args:
            api_key: API key para el LLM (Google Gemini)
            language: Idioma para los prompts ('en' o 'es')
        """
        self.api_key = api_key
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")
        self.test_executor = TestExecutor(api_key=api_key, language=self.language)
        self.project_manager = ProjectManagerService()

    def run_smoke_test(self, instructions: str, url: Optional[str] = None, user_story: Optional[str] = None) -> Dict[str, Any]:
        """Ejecuta un smoke test.

        Args:
            instructions: Instrucciones para el test
            url: URL para la prueba (opcional)
            user_story: Historia de usuario (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        return self.test_executor.run_smoke_test(
            instructions=instructions,
            url=url,
            user_story=user_story
        )

    def run_full_test(self, gherkin_scenario: str, url: Optional[str] = None) -> Dict[str, Any]:
        """Ejecuta un test completo.

        Args:
            gherkin_scenario: Escenario Gherkin a ejecutar
            url: URL para la prueba (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        return self.test_executor.run_full_test(
            gherkin_scenario=gherkin_scenario,
            url=url
        )

    def create_gherkin_scenario(self, instructions: str, url: Optional[str] = None, user_story: Optional[str] = None) -> str:
        """Crea un escenario Gherkin a partir de instrucciones.

        Args:
            instructions: Instrucciones para la prueba
            url: URL para la prueba (opcional)
            user_story: Historia de usuario (opcional)

        Returns:
            str: Escenario Gherkin generado
        """
        return self.test_executor.create_gherkin_scenario(
            instructions=instructions,
            url=url,
            user_story=user_story
        )

    def save_history_to_project(self, project_id: str, suite_id: str, test_history: Dict[str, Any],
                               name: str, description: str, gherkin: str) -> Dict[str, Any]:
        """Guarda un historial de prueba en un proyecto.

        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite de pruebas
            test_history: Historial de la prueba
            name: Nombre del caso de prueba
            description: Descripción del caso de prueba
            gherkin: Escenario Gherkin

        Returns:
            Dict[str, Any]: Objeto del caso de prueba creado
        """
        # Crear un caso de prueba con la información de la prueba
        test_case = self.project_manager.create_test_case(
            project_id=project_id,
            suite_id=suite_id,
            name=name,
            description=description,
            gherkin=gherkin
        )

        # Actualizar el caso de prueba con el historial
        if test_case and "history_json_path" in test_history:
            test_case.history_files.append(test_history["history_json_path"])

            # Guardar el proyecto actualizado
            project = self.project_manager.get_project(project_id)
            if project:
                self.project_manager.save_project(project)

        return test_case

    def generate_code(self, framework: str, gherkin_scenario: str, test_history: Dict[str, Any]) -> str:
        """Genera código de automatización.

        Args:
            framework: Framework para generar el código (selenium, playwright, etc.)
            gherkin_scenario: Escenario Gherkin
            test_history: Historial de la prueba

        Returns:
            str: Código de automatización generado
        """
        from src.Prompts.prompt_manager import PromptManager
        from langchain_google_genai import ChatGoogleGenerativeAI
        import json

        # Crear instancia del modelo
        llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=self.api_key)

        # Preparar datos para la generación
        history_for_code = {
            "urls": test_history.get("urls", []),
            "action_names": test_history.get("action_names", []),
            "detailed_actions": test_history.get("detailed_actions", []),
            "element_xpaths": test_history.get("element_xpaths", {}),
            "extracted_content": test_history.get("extracted_content", []),
            "errors": test_history.get("errors", []),
            "model_actions": test_history.get("model_actions", []),
            "execution_date": test_history.get("execution_date", datetime.now().strftime("%d/%m/%Y %H:%M:%S")),
            "test_id": test_history.get("test_id", "unknown"),
            "screenshot_paths": test_history.get("screenshot_paths", [])
        }

        # Mapeo de frameworks a IDs de prompts
        framework_prompt_ids = {
            "selenium": "code_gen_selenium_pytest",
            "playwright": "code_gen_playwright",
            "cypress": "code_gen_cypress",
            "robot": "code_gen_robot",
            "java": "code_gen_java_selenium"
        }

        # Verificar que el framework solicitado existe
        if framework not in framework_prompt_ids:
            raise ValueError(f"Framework no soportado: {framework}")

        # Obtener el ID del prompt
        prompt_id = framework_prompt_ids[framework]

        # Obtener el prompt en el idioma configurado
        prompt_template = PromptManager.get_prompt(prompt_id, self.language)

        # Obtener base URL del historial
        urls = history_for_code.get("urls", [])
        base_url = urls[0] if urls else "https://example.com"

        # Extraer selectores y acciones
        selectors = json.dumps(history_for_code.get("element_xpaths", {}), indent=2)
        actions = json.dumps(history_for_code.get("action_names", []), indent=2)
        extracted_content = json.dumps(history_for_code.get("extracted_content", []), indent=2)

        # Formatear el prompt con los datos
        prompt = prompt_template.format(
            gherkin_steps=gherkin_scenario,
            base_url=base_url,
            selectors=selectors,
            actions=actions,
            extracted_content=extracted_content
        )

        # Generar código
        response = llm.invoke(prompt)

        # Devolver el código generado
        return response.content

    # === GESTIÓN DE PROYECTOS ===

    def create_project(self, name: str, description: str = "", tags: List[str] = None) -> Dict[str, Any]:
        """Crea un nuevo proyecto.

        Args:
            name: Nombre del proyecto
            description: Descripción del proyecto
            tags: Lista de etiquetas

        Returns:
            Dict[str, Any]: Datos del proyecto creado
        """
        project = self.project_manager.create_project(name=name, description=description, tags=tags or [])
        return project.to_dict()

    def get_project(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene un proyecto por su ID.

        Args:
            project_id: ID del proyecto

        Returns:
            Optional[Dict[str, Any]]: Datos del proyecto o None si no existe
        """
        project = self.project_manager.get_project(project_id)
        return project.to_dict() if project else None

    def get_all_projects(self) -> List[Dict[str, Any]]:
        """Obtiene todos los proyectos.

        Returns:
            List[Dict[str, Any]]: Lista de proyectos
        """
        projects = self.project_manager.get_all_projects()
        return [project.to_dict() for project in projects]

    def update_project(self, project_id: str, name: str = None, description: str = None,
                      tags: List[str] = None) -> Optional[Dict[str, Any]]:
        """Actualiza un proyecto existente.

        Args:
            project_id: ID del proyecto
            name: Nuevo nombre (opcional)
            description: Nueva descripción (opcional)
            tags: Nuevas etiquetas (opcional)

        Returns:
            Optional[Dict[str, Any]]: Datos del proyecto actualizado o None si no existe
        """
        project = self.project_manager.update_project(
            project_id=project_id, name=name, description=description, tags=tags
        )
        return project.to_dict() if project else None

    def delete_project(self, project_id: str) -> bool:
        """Elimina un proyecto.

        Args:
            project_id: ID del proyecto

        Returns:
            bool: True si se eliminó correctamente, False en caso contrario
        """
        return self.project_manager.delete_project(project_id)