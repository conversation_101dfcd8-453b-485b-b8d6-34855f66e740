import os
from typing import Op<PERSON> 
from browser_use import Browser, Agent as BrowserAgent
from langchain_google_genai import ChatGoogleGenerativeAI
#generate_browser_task está en src.Prompts.browser_prompts
from src.Prompts.browser_prompts import generate_browser_task
from src.Utilities.token_optimizer import TokenOptimizer, apply_token_optimizations

async def create_and_run_agent(
    scenario_text: str,
    browser_instance,
    controller_instance,
    api_key: str = None,
    language: Optional[str] = None,  # Added language parameter
    token_optimized: bool = False
):
    """
    Crea, configura y ejecuta un BrowserAgent para un escenario dado.
    Devuelve el historial de la ejecución.

    Args:
        token_optimized: Si True, aplica configuraciones para reducir el consumo de tokens
    """
    if api_key is None:
        api_key = os.environ.get("GOOGLE_API_KEY")

    if not api_key:
        raise ValueError("API key for Google Generative AI not found. Please set the GOOGLE_API_KEY environment variable or pass an api_key.")

    # Use provided language, or fallback to environment variable, then to 'en'
    effective_language = language if language is not None else os.getenv("PROMPT_LANGUAGE", "en")

    agent_task = generate_browser_task(scenario_text, effective_language) # Pass language to generate_browser_task

    # Use optimized Gemini models for cost-efficient automation
    model_name = os.getenv("LLM_MODEL", "gemini-2.0-flash")
    llm_instance = ChatGoogleGenerativeAI(
        model=model_name,
        api_key=api_key
    )

    # Create a smaller, cheaper model for planning if token optimization is enabled
    planner_llm = None
    if token_optimized:
        planner_llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",  # Smaller, cheaper model for planning
            api_key=api_key
        )

    # Configure browser agent using the new API
    browser_agent_config = {
        "task": agent_task,
        "llm": llm_instance,
        "controller": controller_instance,
    }

    # Add token optimization configurations
    if token_optimized:
        browser_agent_config.update({
            "use_vision": False,  # Disable vision to save significant tokens
            "planner_llm": planner_llm,  # Use smaller model for planning
            "planner_interval": 3,  # Plan less frequently
            "max_actions_per_step": 5,  # Reduce from default 10
            "enable_memory": True,  # Enable memory to summarize long conversations
            "memory_config": {
                "memory_interval": 5,  # Summarize more frequently
                "agent_id": "browser_qa_agent"
            }
        })
    else:
        browser_agent_config["use_vision"] = True

    # Create the agent using the new API (without browser_session parameter)
    browser_agent_instance = BrowserAgent(**browser_agent_config)

    history = await browser_agent_instance.run()
    return history
