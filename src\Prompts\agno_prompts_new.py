"""Prompts para agentes de QA."""

import re
from typing import Dict, Any, List, Tuple
import json
import streamlit as st
from langchain_google_genai import ChatGoogleGenerativeAI
import os

from src.Utilities.utils import (
    extract_selectors_from_history,
    analyze_actions)

# Funciones para generación de código y pruebas
def enhance_user_story(user_story: str) -> str:
    """Mejora una historia de usuario para hacerla más clara y completa.
    
    Args:
        user_story: Historia de usuario original
        
    Returns:
        str: Historia de usuario mejorada
    """
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    prompt = f"""
    Tu tarea es mejorar la siguiente historia de usuario para que sea más clara, completa y siga el formato estándar 'Como [rol], quiero [funcionalidad] para [beneficio]'.
    
    Asegúrate de incluir:
    1. El rol específico del usuario (Quién)
    2. La funcionalidad deseada (Qué)
    3. El beneficio o valor esperado (Por qué)
    4. Criterios de aceptación claros y específicos
    5. Ejemplos concretos si es posible
    
    Historia original:
    {user_story}
    
    Proporciona una versión mejorada y expandida que mantenga la intención original pero sea más detallada y útil para los equipos de desarrollo y pruebas.
    """
    
    # Llamar al modelo para mejorar la historia
    response = llm.invoke(prompt)
    return response.content

def generate_manual_test_cases(enhanced_story: str) -> str:
    """Genera casos de prueba manuales a partir de una historia de usuario mejorada.
    
    Args:
        enhanced_story: Historia de usuario mejorada
        
    Returns:
        str: Casos de prueba manuales en formato markdown
    """
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    prompt = f"""
    Tu tarea es generar casos de prueba manuales detallados a partir de la siguiente historia de usuario mejorada.
    
    Historia de usuario mejorada:
    {enhanced_story}
    
    Por favor, genera casos de prueba manuales completos siguiendo estas directrices:
    
    1. Genera al menos 5-10 casos de prueba que cubran todos los criterios de aceptación y flujos principales.
    2. Incluye casos de prueba positivos y negativos.
    3. Utiliza un formato de tabla con las siguientes columnas:
       - Test Case ID (formato TC-001, TC-002, etc.)
       - Test Case Title (breve descripción del objetivo del caso)
       - Preconditions (condiciones previas necesarias)
       - Test Steps (pasos detallados numerados para ejecutar el caso)
       - Expected Results (resultado esperado después de ejecutar los pasos)
       - Test Type (Functional, UI, Performance, etc.)
       - Priority (High, Medium, Low)
    
    Presenta la información en una tabla de Markdown bien formateada.
    """
    
    # Llamar al modelo para generar casos de prueba manuales
    response = llm.invoke(prompt)
    return response.content

def generate_gherkin_scenarios(manual_test_cases_markdown: str) -> str:
    """Generate Gherkin scenarios from manual test cases"""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    prompt = f"""
    Tu tarea es convertir los siguientes casos de prueba manuales en escenarios Gherkin bien estructurados.
    
    Casos de prueba manuales:
    {manual_test_cases_markdown}
    
    Convierte estos casos de prueba en escenarios Gherkin siguiendo estas directrices:
    
    1. Usa las keywords: Feature, Scenario, Given, When, Then, And, But
    2. Crea un Feature que represente la funcionalidad general
    3. Crea escenarios individuales para cada caso de prueba
    4. Mantiene los pasos claros, concisos y en formato imperativo presente
    5. Conserva las mismas precondiciones, acciones y resultados esperados
    6. Usa lenguaje orientado al negocio, no detalles técnicos de implementación
    7. Si hay tablas de ejemplos o escenarios outline, úsalos adecuadamente
    8. Conserva cualquier URL que aparezca en los casos de prueba originales
    9. Representa adecuadamente los casos de prueba negativos
    
    Genera escenarios Gherkin completos y bien formateados que reflejen todos los casos de prueba proporcionados.
    """
    
    # Llamar al modelo para generar escenarios Gherkin
    response = llm.invoke(prompt)
    return response.content

def generate_selenium_pytest_bdd(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Selenium Python code with PyTest BDD."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer XPaths e información relevante del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    action_names = history.get("action_names", [])
    
    # Ejemplo básico, se debería ampliar con la lógica existente
    prompt = f"""
    Genera código de automatización usando Selenium con Python y PyTest BDD basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URLs visitadas: {urls if urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(element_xpaths, indent=2) if element_xpaths else 'No disponible'}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar el patrón Page Object Model
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Incluir configuración para ejecución en diferentes navegadores
    
    Proporciona una implementación completa y funcional.
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content

def generate_playwright_python(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Playwright Python code."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer XPaths e información relevante del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    
    prompt = f"""
    Genera código de automatización usando Playwright con Python basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URLs visitadas: {urls if urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(element_xpaths, indent=2) if element_xpaths else 'No disponible'}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar las capacidades asíncronas de Playwright
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Aprovechar las ventajas de Playwright sobre Selenium
    
    Proporciona una implementación completa y funcional.
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content

def generate_cypress_js(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Cypress JavaScript code."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer XPaths e información relevante del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    
    prompt = f"""
    Genera código de automatización usando Cypress con JavaScript basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URLs visitadas: {urls if urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(element_xpaths, indent=2) if element_xpaths else 'No disponible'}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin usando cucumber/gherkin para Cypress
    2. Utilizar comandos y aserciones propias de Cypress
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente data-* attributes o CSS)
    6. Aprovechar las capacidades de Cypress para testing de UI
    
    Proporciona una implementación completa y funcional incluyendo los archivos de configuración necesarios.
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content

def generate_robot_framework(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Robot Framework code."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer XPaths e información relevante del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    
    prompt = f"""
    Genera código de automatización usando Robot Framework basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URLs visitadas: {urls if urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(element_xpaths, indent=2) if element_xpaths else 'No disponible'}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar keywords de SeleniumLibrary
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Aprovechar la sintaxis declarativa de Robot Framework
    
    Proporciona una implementación completa y funcional.
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content

def generate_java_selenium(gherkin_scenario: str, history: Dict[str, Any]) -> str:
    """Generate Java Selenium with Cucumber code."""
    llm = ChatGoogleGenerativeAI(model=os.getenv("LLM_MODEL", "gemini-2.0-flash"), api_key=os.environ.get("GOOGLE_API_KEY"))
    
    # Extraer XPaths e información relevante del historial
    element_xpaths = history.get("element_xpaths", {})
    urls = history.get("urls", [])
    
    prompt = f"""
    Genera código de automatización usando Selenium con Java y Cucumber basado en este escenario Gherkin:
    
    ```gherkin
    {gherkin_scenario}
    ```
    
    Información adicional de la ejecución:
    - URLs visitadas: {urls if urls else ['No disponible']}
    - XPaths de elementos: {json.dumps(element_xpaths, indent=2) if element_xpaths else 'No disponible'}
    
    El código debe:
    1. Implementar todos los pasos del escenario Gherkin
    2. Utilizar el patrón Page Object Model
    3. Incluir manejo de errores y timeouts
    4. Comentar cualquier parte compleja
    5. Usar selectores estables (preferentemente XPath o CSS)
    6. Incluir estructura de proyecto Maven o Gradle
    
    Proporciona una implementación completa y funcional incluyendo todos los archivos necesarios (glue code, step definitions, etc).
    """
    
    # Llamar al modelo para generar código
    response = llm.invoke(prompt)
    return response.content