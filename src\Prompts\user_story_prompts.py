"""User story enhancement prompts in multiple languages."""

from typing import Dict

prompts: Dict[str, Dict[str, str]] = {
    "user_story_enhance": {
        "en": """
        Your task is to improve the following user story to be clearer, more complete, and follow the standard format 'As a [role], I want [functionality] to [benefit]'.
        
        Make sure to include:
        1. The specific user role (Who)
        2. The desired functionality (What)
        3. The expected benefit or value (Why)
        4. Clear and specific acceptance criteria
        5. Concrete examples if possible
        
        Original story:
        {user_story}
        
        Provide an improved and expanded version that maintains the original intention but is more detailed and useful for development and testing teams.
        """,
        
        "es": """
        Tu tarea es mejorar la siguiente historia de usuario para que sea más clara, completa y siga el formato estándar 'Como [rol], quiero [funcionalidad] para [beneficio]'.
        
        Asegúrate de incluir:
        1. El rol específico del usuario (Quién)
        2. La funcionalidad deseada (Qué)
        3. El beneficio o valor esperado (Por qué)
        4. Criterios de aceptación claros y específicos
        5. Ejemplos concretos si es posible
        
        Historia original:
        {user_story}
        
        Proporciona una versión mejorada y expandida que mantenga la intención original pero sea más detallada y útil para los equipos de desarrollo y pruebas.
        """
    }
}