# Agents QA - Automatización de Pruebas con IA

Herramienta de automatización de pruebas impulsada por IA que transforma historias de usuario en código de automatización ejecutable, permitiendo la generación y ejecución de pruebas automatizadas a partir de instrucciones en lenguaje natural.

## Características Principales

- **Full Test**: Proceso completo desde historias de usuario hasta código de automatización
- **Smoke Test**: Ejecución directa de pruebas sin pasos intermedios
- **Múltiples Frameworks**: Soporte para diversos frameworks de automatización
- **Ejecución en Navegador Real**: Pruebas ejecutadas en navegador real
- **Gestión de Proyectos**: Organización de pruebas en proyectos y suites
- **Historial de Tests**: Registro detallado de la ejecución de pruebas

## Requisitos Previos

- Python 3.9 o superior
- Google Gemini API Key

## Instalación

### Configuración del Entorno Virtual (Recomendado)

Es altamente recomendable usar un entorno virtual para evitar conflictos de dependencias:

#### En Windows (PowerShell):
```powershell
# 1. Crear entorno virtual
python -m venv .venv

# 2. Activar entorno virtual
.\.venv\Scripts\Activate.ps1

# 3. Instalar dependencias
pip install -r requirements.txt
```

#### En Linux/MacOS (Terminal):
```bash
# 1. Crear entorno virtual
python3 -m venv .venv

# 2. Activar entorno virtual
source .venv/bin/activate

# 3. Instalar dependencias
pip install -r requirements.txt
```

#### Alternativas de Activación:

**Windows (si PowerShell no funciona):**
```cmd
# Usando Command Prompt (CMD)
.venv\Scripts\activate.bat
```

**macOS (si python3 no está disponible):**
```bash
# Usando python en lugar de python3
python -m venv .venv
source .venv/bin/activate
```

> **Nota**: Si encuentras problemas con la política de ejecución en PowerShell, ejecuta:
> ```powershell
> Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
> ```

### Instalación Directa (Sin entorno virtual)

1. Instala las dependencias:

```bash
pip install -r requirements.txt
```

2. Configura la API Key de Google Gemini:

Crea un archivo `.env` en la raíz del proyecto y añade tu API Key:

```
GOOGLE_API_KEY=tu_api_key_aqui
GEMINI_API_KEY=tu_api_key_aqui
LLM_MODEL=ModeloLLM (Default si no esta seteado es gemini 2.0 flash)
PROMPT_LANGUAGE=es (Idioma del prompt)
```

### Ejecución de la Aplicación

Una vez instaladas las dependencias y configurado el archivo `.env`:

```powershell
# Asegúrate de que el entorno virtual esté activado
.\.venv\Scripts\Activate.ps1

# Ejecutar la aplicación web
streamlit run app.py
```

La aplicación se abrirá automáticamente en tu navegador en `http://localhost:8501`

## Modo de Uso

### Interfaz Web (Streamlit)

La aplicación proporciona una interfaz web completa para gestionar todas las funcionalidades:

```bash
streamlit run app.py
```

Esto abrirá la interfaz web en tu navegador predeterminado, donde podrás:

1. Seleccionar entre modo Full Test o Smoke Test
2. Ingresar historias de usuario
3. Generar y editar escenarios Gherkin
4. Ejecutar pruebas en navegador real
5. Generar código de automatización 
6. Gestionar proyectos y suites de pruebas
7. Ver el historial de ejecuciones previas

### Línea de Comandos (CLI)

La aplicación también puede ejecutarse desde la línea de comandos:

#### Ejecutar un Smoke Test:

```bash
python cli.py smoke --url "https://www.ejemplo.com" --instructions "Iniciar sesión con usuario 'admin' y contraseña 'password'" --user-story "Como administrador, quiero acceder al panel de control"
```

#### Generar código de automatización desde una ejecución previa:

```bash
python cli.py generate-code --history-path "tests/smoke_test_YYYYMMDDHHMMSS/history.json" --framework "playwright"
```

#### Generar código durante la ejecución de un Smoke Test:

```bash
python cli.py smoke --url "https://www.ejemplo.com" --instructions "Iniciar sesión con usuario 'admin'" --generate-code "playwright"
```

## Frameworks Soportados

La aplicación soporta la generación de código de automatización para los siguientes frameworks:

- **Selenium + PyTest BDD (Python)**: Popular framework Python combinando Selenium WebDriver con PyTest BDD para desarrollo dirigido por comportamiento.
- **Playwright (Python)**: Framework moderno con soporte asíncrono integrado y pruebas cross-browser.
- **Cypress (JavaScript)**: Framework moderno de JavaScript para pruebas end-to-end con recarga en tiempo real.
- **Robot Framework**: Framework keyword-driven que utiliza sintaxis tabular simple.
- **Selenium + Cucumber (Java)**: Combinación robusta de Selenium WebDriver con Cucumber para Java.

## Ejemplo de Uso Completo

### Modo Full Test:

1. Ingresa una historia de usuario
2. Mejora la historia con el botón "Mejorar Historia"
3. Genera casos de prueba manuales
4. Genera escenarios Gherkin desde esos casos
5. Ejecuta las pruebas en navegador real
6. Genera código para el framework de tu elección
7. Guarda los resultados en tu proyecto

### Modo Smoke Test:

1. Ingresa la URL de la aplicación a probar
2. Ingresa las instrucciones de prueba en lenguaje natural
3. Ejecuta la prueba directamente
4. Visualiza los resultados y capturas de pantalla
5. Genera código de automatización si lo deseas

## Organización del Proyecto

- **app.py**: Aplicación principal con interfaz Streamlit
- **cli.py**: Interfaz de línea de comandos
- **src/**: Código fuente de la aplicación
  - **Agents/**: Agentes de automatización
  - **Prompts/**: Prompts para modelos de lenguaje
  - **UI/**: Componentes de la interfaz de usuario
  - **Utilities/**: Utilidades y servicios
- **tests/**: Directorio donde se almacenan los resultados de las pruebas

## Solución de Problemas Comunes

### Error de Política de Ejecución en PowerShell
Si encuentras el error "cannot be loaded because running scripts is disabled on this system":
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Error de Dependencias
Si tienes problemas con las dependencias, intenta:
```powershell
# Actualizar pip
python -m pip install --upgrade pip

# Reinstalar dependencias
pip install -r requirements.txt --force-reinstall
```

### Error de API Key
Asegúrate de que tu archivo `.env` esté en la raíz del proyecto y contenga una API key válida de Google Gemini.

### Problemas con el Navegador
La aplicación requiere que tengas Chrome o Chromium instalado para las pruebas automatizadas.

---

**Desarrollado con ❤️ para matar a los QA**