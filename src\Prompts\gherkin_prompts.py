"""Gherkin scenario generation prompts in multiple languages."""

from typing import Dict

prompts: Dict[str, Dict[str, str]] = {
    "gherkin_generate": {
        "en": """
        Your task is to convert the following manual test cases into well-structured Gherkin scenarios.
        
        Manual test cases:
        {manual_test_cases_markdown}
        
        Convert these test cases into Gherkin scenarios following these guidelines:
        
        1. Use the keywords: Feature, Scenario, Given, When, Then, And, But
        2. Create a Feature that represents the general functionality
        3. Create individual scenarios for each test case
        4. Keep the steps clear, concise, and in present imperative format
        5. Maintain the same preconditions, actions, and expected results
        6. Use business-oriented language, not technical implementation details
        7. If there are example tables or scenario outlines, use them appropriately
        8. Preserve any URLs that appear in the original test cases
        9. Adequately represent negative test cases
        
        Generate complete and well-formatted Gherkin scenarios that reflect all the provided test cases.
        """,
        
        "es": """
        Tu tarea es convertir los siguientes casos de prueba manuales en escenarios Gherkin bien estructurados.
        
        Casos de prueba manuales:
        {manual_test_cases_markdown}
        
        Convierte estos casos de prueba en escenarios Gherkin siguiendo estas directrices:
        
        1. Usa las keywords: Feature, Scenario, Given, When, Then, And, But
        2. Crea un Feature que represente la funcionalidad general
        3. Crea escenarios individuales para cada caso de prueba
        4. Mantiene los pasos claros, concisos y en formato imperativo presente
        5. Conserva las mismas precondiciones, acciones y resultados esperados
        6. Usa lenguaje orientado al negocio, no detalles técnicos de implementación
        7. Si hay tablas de ejemplos o escenarios outline, úsalos adecuadamente
        8. Conserva cualquier URL que aparezca en los casos de prueba originales
        9. Representa adecuadamente los casos de prueba negativos
        
        Genera escenarios Gherkin completos y bien formateados que reflejen todos los casos de prueba proporcionados.
        """
    }
}